#!/bin/bash

# SSL 证书生成脚本
# 用于开发和测试环境生成自签名证书

set -e

echo "🔐 生成 SSL 证书..."

# 创建 SSL 目录
mkdir -p ssl

# 检查域名参数
DOMAIN=${1:-localhost}
echo "📝 域名: $DOMAIN"

# 生成私钥
echo "🔑 生成私钥..."
openssl genrsa -out ssl/key.pem 2048

# 生成证书签名请求
echo "📄 生成证书签名请求..."
openssl req -new -key ssl/key.pem -out ssl/cert.csr -subj "/C=CN/ST=Beijing/L=Beijing/O=SoulVoice/OU=IT/CN=$DOMAIN"

# 生成自签名证书
echo "📜 生成自签名证书..."
openssl x509 -req -days 365 -in ssl/cert.csr -signkey ssl/key.pem -out ssl/cert.pem

# 清理临时文件
rm ssl/cert.csr

# 设置权限
chmod 600 ssl/key.pem
chmod 644 ssl/cert.pem

echo "✅ SSL 证书生成完成！"
echo "📁 证书位置:"
echo "   私钥: ssl/key.pem"
echo "   证书: ssl/cert.pem"
echo ""
echo "⚠️  注意: 这是自签名证书，仅用于开发测试"
echo "🌐 生产环境请使用 Let's Encrypt 或购买正式证书"
echo ""
echo "🚀 使用 HTTPS 部署:"
echo "   docker-compose -f docker-compose.https.yml up -d"
