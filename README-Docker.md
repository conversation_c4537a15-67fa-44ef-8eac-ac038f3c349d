# SoulVoice Docker 部署指南

本指南将帮助您使用 Docker 快速部署 SoulVoice 应用，实现通过自定义域名访问 API，隐藏 Supabase 地址。

## 🏗️ 架构说明

### 部署架构
```
用户请求 → Nginx (容器) → 静态文件 / API 代理 → Supabase Functions
```

### 安全特性
- ✅ 隐藏真实 Supabase URL
- ✅ 通过自定义域名访问 API (`https://域名/functions/v1/tts`)
- ✅ Nginx 反向代理保护后端服务
- ✅ CORS 和安全头配置

## 🚀 快速开始

### 1. 环境要求
- Docker >= 20.10
- Docker Compose >= 2.0

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.docker .env

# 编辑配置文件
vim .env
```

配置内容：
```env
# Supabase 配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key

# 域名配置
DOMAIN=your-domain.com
```

### 3. 一键部署
```bash
# 使用部署脚本
./deploy.sh

# 或手动部署
docker-compose up -d
```

### 4. 验证部署
```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 健康检查
curl http://localhost/health
```

## 📁 文件说明

### 核心文件
- `Dockerfile` - 多阶段构建配置
- `nginx.conf.template` - Nginx 配置模板
- `docker-compose.yml` - 容器编排配置
- `docker-entrypoint.sh` - 容器启动脚本

### 配置文件
- `.env.docker` - 环境变量模板
- `.dockerignore` - 构建忽略文件
- `deploy.sh` - 快速部署脚本

## 🔧 高级配置

### HTTPS 配置
如需启用 HTTPS，修改 `docker-compose.yml`：

```yaml
services:
  soulvoice:
    ports:
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    environment:
      - SSL_ENABLED=true
```

### 自定义 Nginx 配置
编辑 `nginx.conf.template` 文件自定义配置。

### 资源限制
在 `docker-compose.yml` 中调整资源限制：

```yaml
deploy:
  resources:
    limits:
      memory: 1G
      cpus: '1.0'
```

## 🛠️ 常用命令

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 更新部署
docker-compose pull && docker-compose up -d
```

## 🔍 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 检查日志
   docker-compose logs soulvoice
   
   # 检查环境变量
   docker-compose config
   ```

2. **API 代理不工作**
   ```bash
   # 检查 Nginx 配置
   docker exec soulvoice-app nginx -t
   
   # 查看 Nginx 日志
   docker exec soulvoice-app tail -f /var/log/nginx/error.log
   ```

3. **前端无法访问**
   ```bash
   # 检查端口映射
   docker-compose ps
   
   # 测试健康检查
   curl http://localhost/health
   ```

### 调试模式
启用调试模式查看详细日志：

```bash
# 前台运行查看日志
docker-compose up

# 进入容器调试
docker exec -it soulvoice-app sh
```

## 🌐 生产部署建议

### 1. 使用 HTTPS
- 配置 SSL 证书
- 使用 Let's Encrypt 自动续期

### 2. 域名配置
- 设置 DNS A 记录指向服务器 IP
- 配置 CDN 加速（可选）

### 3. 监控和日志
- 配置日志收集
- 设置健康检查监控
- 配置告警通知

### 4. 安全加固
- 定期更新镜像
- 配置防火墙规则
- 启用访问日志分析

## 📊 性能优化

### 1. 缓存策略
- 静态资源长期缓存
- API 响应适当缓存

### 2. 压缩配置
- 启用 Gzip 压缩
- 优化图片资源

### 3. 负载均衡
- 多实例部署
- 使用负载均衡器

## 🔗 相关链接

- [Docker 官方文档](https://docs.docker.com/)
- [Nginx 配置指南](https://nginx.org/en/docs/)
- [Supabase 文档](https://supabase.com/docs)

## 📞 技术支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查 GitHub Issues
3. 联系技术支持团队
