# Docker Compose HTTPS 配置
# 用于生产环境 HTTPS 部署

version: '3.8'

services:
  soulvoice:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: soulvoice-app-https
    ports:
      - "80:80"   # HTTP 重定向
      - "443:443" # HTTPS
    environment:
      # Supabase 配置
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      
      # 域名配置
      - DOMAIN=${DOMAIN}
      
      # HTTPS 配置
      - SSL_ENABLED=true
    
    volumes:
      # SSL 证书挂载
      - ./ssl:/etc/nginx/ssl:ro
      # 使用 HTTPS 配置模板
      - ./nginx-https.conf.template:/etc/nginx/templates/default.conf.template:ro
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "-k", "https://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 重启策略
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# 网络配置
networks:
  default:
    name: soulvoice-https-network
