#!/bin/bash

# SoulVoice 快速部署脚本

set -e

echo "🚀 SoulVoice 部署"

# 检查环境变量文件
if [ ! -f ".env" ]; then
    if [ -f ".env.docker" ]; then
        cp .env.docker .env
        echo "⚠️  请编辑 .env 文件，填入 Supabase 配置"
        exit 1
    else
        echo "❌ 请创建 .env 文件"
        exit 1
    fi
fi

# 验证环境变量
source .env
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_ANON_KEY" ]; then
    echo "❌ 请配置 SUPABASE_URL 和 SUPABASE_ANON_KEY"
    exit 1
fi

echo "📦 构建镜像..."
docker-compose build

echo "🚀 启动服务..."
docker-compose down
docker-compose up -d

echo "⏳ 等待启动..."
sleep 10

# 检查状态
if docker-compose ps | grep -q "Up"; then
    echo "✅ 部署成功！"
    echo "🔗 访问: http://服务器IP:8000"
    echo "📡 API: http://服务器IP:8000/functions/v1/"
else
    echo "❌ 部署失败"
    docker-compose logs
    exit 1
fi
