#!/bin/bash

# SoulVoice Docker 快速部署脚本

set -e

echo "🚀 SoulVoice Docker 部署脚本"
echo "================================"

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    if [ -f ".env.docker" ]; then
        echo "📝 复制环境变量模板..."
        cp .env.docker .env
        echo "⚠️  请编辑 .env 文件，填入正确的 Supabase 配置"
        echo "   SUPABASE_URL=https://your-project.supabase.co"
        echo "   SUPABASE_ANON_KEY=your-anon-key"
        echo "   DOMAIN=your-domain.com"
        echo ""
        read -p "是否已配置完成？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "请先配置 .env 文件后再运行此脚本"
            exit 1
        fi
    else
        echo "❌ 未找到环境变量文件，请创建 .env 文件"
        exit 1
    fi
fi

# 读取环境变量
source .env

# 验证必要的环境变量
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_ANON_KEY" ]; then
    echo "❌ 请在 .env 文件中配置 SUPABASE_URL 和 SUPABASE_ANON_KEY"
    exit 1
fi

echo "📦 构建 Docker 镜像..."
docker-compose build

echo "🔄 停止现有容器..."
docker-compose down

echo "🚀 启动服务..."
docker-compose up -d

echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
if docker-compose ps | grep -q "Up"; then
    echo "✅ 部署成功！"
    echo ""
    echo "🔗 访问地址:"
    echo "   Web 应用: http://${DOMAIN:-localhost}"
    echo "   API 端点: http://${DOMAIN:-localhost}/functions/v1/"
    echo "   健康检查: http://${DOMAIN:-localhost}/health"
    echo ""
    echo "📊 查看日志: docker-compose logs -f"
    echo "🛑 停止服务: docker-compose down"
else
    echo "❌ 部署失败，请检查日志:"
    docker-compose logs
    exit 1
fi
