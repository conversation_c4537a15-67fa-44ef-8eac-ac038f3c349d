#!/bin/sh

# Docker 容器启动脚本
# 用于处理环境变量替换和启动 Nginx

set -e

echo "🚀 启动 SoulVoice 容器..."

# 检查必要的环境变量
if [ -z "$SUPABASE_URL" ]; then
    echo "❌ 错误: SUPABASE_URL 环境变量未设置"
    exit 1
fi

if [ -z "$SUPABASE_ANON_KEY" ]; then
    echo "❌ 错误: SUPABASE_ANON_KEY 环境变量未设置"
    exit 1
fi

# 清理 SUPABASE_URL（移除末尾的斜杠）
export SUPABASE_URL=$(echo "$SUPABASE_URL" | sed 's:/*$::')

echo "📝 配置信息:"
echo "   域名: ${DOMAIN:-localhost}"
echo "   Supabase URL: $SUPABASE_URL"
echo "   API 代理: /functions/v1/* -> $SUPABASE_URL/functions/v1/*"

# 使用 envsubst 替换 Nginx 配置模板中的环境变量
echo "🔧 生成 Nginx 配置..."
envsubst '${SUPABASE_URL} ${DOMAIN}' < /etc/nginx/templates/default.conf.template > /etc/nginx/conf.d/default.conf

# 验证 Nginx 配置
echo "✅ 验证 Nginx 配置..."
nginx -t

echo "🌟 SoulVoice 容器启动完成!"
echo "🔗 访问地址: http://${DOMAIN:-localhost}"
echo "📡 API 端点: http://${DOMAIN:-localhost}/functions/v1/"

# 执行传入的命令
exec "$@"
