#!/bin/sh

set -e

echo "🚀 启动 SoulVoice..."

# 检查环境变量
if [ -z "$SUPABASE_URL" ]; then
    echo "❌ SUPABASE_URL 未设置"
    exit 1
fi

# 清理 URL
export SUPABASE_URL=$(echo "$SUPABASE_URL" | sed 's:/*$::')

echo "📝 Supabase URL: $SUPABASE_URL"
echo "📡 API 端点: /functions/v1/*"

# 生成 Nginx 配置
envsubst '${SUPABASE_URL}' < /etc/nginx/templates/default.conf.template > /etc/nginx/conf.d/default.conf

# 验证配置
nginx -t

echo "✅ 启动完成"

exec "$@"
