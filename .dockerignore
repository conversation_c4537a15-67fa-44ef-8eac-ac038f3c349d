# Docker 构建忽略文件
# 优化构建性能和减少镜像大小

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# 测试和覆盖率
coverage/
.nyc_output/
*.lcov

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/

# 文档
README.md
docs/
*.md

# Supabase 本地文件
supabase/.temp/
supabase/logs/

# 脚本文件（部署时不需要）
scripts/

# 测试文件
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# SSL 证书（通过 volume 挂载）
ssl/
*.pem
*.crt
*.key

# Docker 相关文件
docker-compose*.yml
Dockerfile*
.dockerignore
