#!/bin/bash

# Docker 部署测试脚本
# 用于验证部署是否成功

set -e

echo "🧪 SoulVoice Docker 部署测试"
echo "============================"

# 检查环境变量
if [ ! -f ".env" ]; then
    echo "❌ 未找到 .env 文件"
    exit 1
fi

source .env

# 检查必要的环境变量
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_ANON_KEY" ]; then
    echo "❌ 环境变量配置不完整"
    exit 1
fi

DOMAIN=${DOMAIN:-localhost}
BASE_URL="http://$DOMAIN"

echo "🔍 测试目标: $BASE_URL"

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 测试健康检查
echo "🏥 测试健康检查..."
if curl -f "$BASE_URL/health" > /dev/null 2>&1; then
    echo "✅ 健康检查通过"
else
    echo "❌ 健康检查失败"
    exit 1
fi

# 测试主页
echo "🏠 测试主页访问..."
if curl -f "$BASE_URL/" > /dev/null 2>&1; then
    echo "✅ 主页访问正常"
else
    echo "❌ 主页访问失败"
    exit 1
fi

# 测试 API 代理
echo "🔌 测试 API 代理..."
if curl -f "$BASE_URL/functions/v1/health" > /dev/null 2>&1; then
    echo "✅ API 代理工作正常"
else
    echo "⚠️  API 代理可能需要检查（这可能是正常的，如果 Supabase Functions 未部署）"
fi

# 测试静态资源
echo "📦 测试静态资源..."
if curl -f "$BASE_URL/favicon.ico" > /dev/null 2>&1; then
    echo "✅ 静态资源访问正常"
else
    echo "⚠️  静态资源访问异常"
fi

# 检查容器状态
echo "📊 检查容器状态..."
if docker-compose ps | grep -q "Up"; then
    echo "✅ 容器运行正常"
    docker-compose ps
else
    echo "❌ 容器状态异常"
    docker-compose ps
    exit 1
fi

# 检查日志中的错误
echo "📝 检查错误日志..."
ERROR_COUNT=$(docker-compose logs --tail=50 2>&1 | grep -i error | wc -l)
if [ "$ERROR_COUNT" -eq 0 ]; then
    echo "✅ 无错误日志"
else
    echo "⚠️  发现 $ERROR_COUNT 条错误日志，请检查:"
    docker-compose logs --tail=10 | grep -i error
fi

echo ""
echo "🎉 部署测试完成！"
echo "🔗 访问地址:"
echo "   Web 应用: $BASE_URL"
echo "   API 端点: $BASE_URL/functions/v1/"
echo "   健康检查: $BASE_URL/health"
echo ""
echo "📊 性能测试:"
echo "   curl -w \"@curl-format.txt\" -o /dev/null -s $BASE_URL/"
