# Nginx 简化配置 - 云服务器部署

client_max_body_size 10M;

# 基础压缩
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

server {
    listen 80;
    server_name _;

    # 基础安全头
    add_header X-Content-Type-Options "nosniff" always;
    
    # 静态文件根目录
    root /usr/share/nginx/html;
    index index.html;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1d;
        try_files $uri =404;
    }
    
    # API 代理到 Supabase Functions
    location /functions/ {
        rewrite ^/functions/(.*)$ /$1 break;
        proxy_pass ${SUPABASE_URL}/functions/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        # CORS 支持
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Authorization, Content-Type" always;

        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
