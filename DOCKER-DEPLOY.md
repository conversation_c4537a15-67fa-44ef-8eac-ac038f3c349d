# SoulVoice Docker 云服务器部署

## 🚀 快速部署

### 1. 准备环境变量
```bash
cp .env.docker .env
vim .env
```

配置内容：
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
```

### 2. 一键部署
```bash
./deploy.sh
```

### 3. 访问应用
- **Web 应用**: `http://服务器IP:8000`
- **API 端点**: `http://服务器IP:8000/functions/v1/tts`

## 📁 核心文件

- `Dockerfile` - 容器构建配置
- `docker-compose.yml` - 服务编排
- `nginx.conf.template` - Nginx 配置
- `.env.docker` - 环境变量模板

## 🔧 常用命令

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f

# 重新构建
docker-compose build --no-cache
```

## 🌐 API 使用

部署后，原来的 Supabase 地址：
```
https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1/tts
```

变成：
```
http://***********:8000/functions/v1/tts
```

完全隐藏了 Supabase 地址，提高安全性！

## 🛠️ 故障排除

1. **容器启动失败**
   ```bash
   docker-compose logs
   ```

2. **端口被占用**
   ```bash
   # 修改 docker-compose.yml 中的端口映射
   ports:
     - "8001:80"  # 改为其他端口
   ```

3. **API 代理不工作**
   ```bash
   # 检查环境变量
   docker-compose config
   ```
