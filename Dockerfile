# 多阶段构建 Dockerfile for SoulVoice
# Stage 1: 构建前端应用
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 安装所有依赖（包括开发依赖，构建需要）
RUN npm ci

# 复制源代码
COPY . .

# 复制生产环境配置
COPY .env.production .env

# 构建应用（使用相对路径的 API URL）
RUN npm run build

# Stage 2: 生产环境
FROM nginx:alpine

# 安装 gettext 用于环境变量替换
RUN apk add --no-cache gettext

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制 Nginx 配置模板
COPY nginx.conf.template /etc/nginx/templates/default.conf.template

# 复制启动脚本
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# 设置环境变量默认值（运行时会被覆盖）
ENV SUPABASE_URL=""
ENV SUPABASE_ANON_KEY=""
ENV DOMAIN=localhost

# 暴露端口
EXPOSE 80

# 启动脚本
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
